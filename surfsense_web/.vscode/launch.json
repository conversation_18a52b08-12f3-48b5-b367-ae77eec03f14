{"version": "0.2.0", "configurations": [{"name": "Next.js: debug client-side", "type": "chrome", "request": "launch", "url": "http://localhost:3000", "webRoot": "${workspaceFolder}"}, {"name": "Next.js: debug server-side", "type": "node-terminal", "request": "launch", "command": "pnpm run debug:server", "skipFiles": ["<node_internals>/**"]}, {"name": "Next.js: debug full stack", "type": "node-terminal", "request": "launch", "command": "pnpm run debug", "serverReadyAction": {"pattern": "- Local:.+(https?://.+)", "uriFormat": "%s", "action": "debugWithChrome"}, "skipFiles": ["<node_internals>/**"]}]}