[project]
name = "surf-new-backend"
version = "0.0.7"
description = "SurfSense Backend"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "alembic>=1.13.0",
    "asyncpg>=0.30.0",
    "chonkie[all]>=1.0.6",
    "discord-py>=2.5.2",
    "docling>=2.15.0",
    "fastapi>=0.115.8",
    "fastapi-users[oauth,sqlalchemy]>=14.0.1",
    "firecrawl-py>=1.12.0",
    "github3.py==4.0.1",
    "langchain-community>=0.3.17",
    "langchain-unstructured>=0.1.6",
    "langgraph>=0.3.29",
    "linkup-sdk>=0.2.4",
    "litellm>=1.61.4,<1.70.0",
    "llama-cloud-services>=0.6.25",
    "markdownify>=0.14.1",
    "notion-client>=2.3.0",
    "pgvector>=0.3.6",
    "playwright>=1.50.0",
    "python-ffmpeg>=2.0.12",
    "rerankers[flashrank]>=0.7.1",
    "sentence-transformers>=3.4.1",
    "slack-sdk>=3.34.0",
    "static-ffmpeg>=2.13",
    "tavily-python>=0.3.2",
    "unstructured-client>=0.30.0",
    "unstructured[all-docs]>=0.16.25",
    "uvicorn[standard]>=0.34.0",
    "validators>=0.34.0",
    "youtube-transcript-api>=1.0.3",
]

[dependency-groups]
dev = [
    "ruff>=0.12.5",
]

[tool.ruff]
# Exclude a variety of commonly ignored directories.
exclude = [
    ".bzr",
    ".direnv",
    ".eggs",
    ".git",
    ".git-rewrite",
    ".hg",
    ".ipynb_checkpoints",
    ".mypy_cache",
    ".nox",
    ".pants.d",
    ".pyenv",
    ".pytest_cache",
    ".pytype",
    ".ruff_cache",
    ".svn",
    ".tox",
    ".venv",
    ".vscode",
    "__pypackages__",
    "_build",
    "buck-out",
    "build",
    "dist",
    "node_modules",
    "site-packages",
    "venv",
]

line-length = 88
indent-width = 4

# Python 3.12
target-version = "py312"

[tool.ruff.lint]
select = [
    "E4",     # pycodestyle errors
    "E7",     # pycodestyle errors  
    "E9",     # pycodestyle errors
    "F",      # Pyflakes
    "I",      # isort
    "N",      # pep8-naming
    "UP",     # pyupgrade
    "B",      # flake8-bugbear
    "C4",     # flake8-comprehensions
    "T20",    # flake8-print
    "SIM",    # flake8-simplify
    "RUF",    # Ruff-specific rules
]

ignore = [
    "E501",   # Line too long (handled by formatter)
    "B008",   # Do not perform function calls in argument defaults
    "T201",   # Print found (allow print statements)
    "RUF012", # Mutable class attributes should be annotated with `typing.ClassVar`
]

extend-select = ["I"]

# Allow fix for all enabled rules (when `--fix`) is provided.
fixable = ["ALL"]
unfixable = []

# Allow unused variables when underscore-prefixed.
dummy-variable-rgx = "^(_+|(_+[a-zA-Z0-9_]*[a-zA-Z0-9]+?))$"

[tool.ruff.format]
# Use double quotes for strings.
quote-style = "double"

# Indent with spaces, rather than tabs.
indent-style = "space"

# Respect magic trailing commas.
skip-magic-trailing-comma = false

# Automatically detect the appropriate line ending.
line-ending = "auto"


[tool.ruff.lint.isort]
# Group imports by type
known-first-party = ["app"]
force-single-line = false
combine-as-imports = true
