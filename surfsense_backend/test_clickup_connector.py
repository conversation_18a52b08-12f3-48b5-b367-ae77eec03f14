#!/usr/bin/env python3
"""
Test script for ClickUp Connector

This script tests the basic functionality of the ClickUp connector
without requiring a real API token.
"""

import sys
import os

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from connectors.clickup_connector import ClickUpConnector


def test_clickup_connector_initialization():
    """Test ClickUp connector initialization."""
    print("Testing ClickUp connector initialization...")
    
    # Test initialization without token
    connector = ClickUpConnector()
    assert connector.api_token is None
    assert connector.base_url == "https://api.clickup.com/api/v2"
    print("✓ Initialization without token works")
    
    # Test initialization with token
    test_token = "pk_test_token_123"
    connector = ClickUpConnector(api_token=test_token)
    assert connector.api_token == test_token
    print("✓ Initialization with token works")
    
    # Test setting token
    new_token = "pk_new_token_456"
    connector.set_api_token(new_token)
    assert connector.api_token == new_token
    print("✓ Setting token works")


def test_clickup_connector_headers():
    """Test ClickUp connector header generation."""
    print("\nTesting ClickUp connector headers...")
    
    connector = ClickUpConnector()
    
    # Test headers without token (should raise ValueError)
    try:
        connector.get_headers()
        assert False, "Should have raised ValueError"
    except ValueError as e:
        assert "not initialized" in str(e)
        print("✓ Headers without token raises ValueError")
    
    # Test headers with token
    test_token = "pk_test_token_123"
    connector.set_api_token(test_token)
    headers = connector.get_headers()
    
    expected_headers = {
        "Content-Type": "application/json",
        "Authorization": test_token,
    }
    
    assert headers == expected_headers
    print("✓ Headers with token are correct")


def test_clickup_connector_api_request_validation():
    """Test ClickUp connector API request validation."""
    print("\nTesting ClickUp connector API request validation...")
    
    connector = ClickUpConnector()
    
    # Test API request without token (should raise ValueError)
    try:
        connector.make_api_request("team")
        assert False, "Should have raised ValueError"
    except ValueError as e:
        assert "not initialized" in str(e)
        print("✓ API request without token raises ValueError")


def test_date_range_functionality():
    """Test date range functionality."""
    print("\nTesting date range functionality...")
    
    from datetime import datetime
    
    # Test date conversion logic
    start_date = "2024-01-01"
    end_date = "2024-01-31"
    
    start_timestamp = int(
        datetime.strptime(start_date, "%Y-%m-%d").timestamp() * 1000
    )
    end_timestamp = int(
        datetime.strptime(end_date, "%Y-%m-%d").timestamp() * 1000
    )
    
    assert start_timestamp > 0
    assert end_timestamp > start_timestamp
    print("✓ Date conversion logic works")


def test_schema_validation():
    """Test schema validation for ClickUp connector."""
    print("\nTesting schema validation...")
    
    from schemas.search_source_connector import SearchSourceConnectorCreate
    from db import SearchSourceConnectorType
    
    # Test valid ClickUp connector configuration
    valid_config = {
        "name": "Test ClickUp Connector",
        "connector_type": SearchSourceConnectorType.CLICKUP_CONNECTOR,
        "is_indexable": True,
        "config": {
            "CLICKUP_API_TOKEN": "pk_test_token_123"
        }
    }
    
    try:
        connector = SearchSourceConnectorCreate(**valid_config)
        assert connector.name == "Test ClickUp Connector"
        assert connector.connector_type == SearchSourceConnectorType.CLICKUP_CONNECTOR
        assert connector.config["CLICKUP_API_TOKEN"] == "pk_test_token_123"
        print("✓ Valid ClickUp connector configuration passes validation")
    except Exception as e:
        print(f"✗ Valid configuration failed: {e}")
    
    # Test invalid ClickUp connector configuration (missing API token)
    invalid_config = {
        "name": "Test ClickUp Connector",
        "connector_type": SearchSourceConnectorType.CLICKUP_CONNECTOR,
        "is_indexable": True,
        "config": {}
    }
    
    try:
        connector = SearchSourceConnectorCreate(**invalid_config)
        print("✗ Invalid configuration should have failed")
    except Exception as e:
        assert "CLICKUP_API_TOKEN" in str(e)
        print("✓ Invalid ClickUp connector configuration fails validation")


def main():
    """Run all tests."""
    print("Running ClickUp Connector Tests")
    print("=" * 50)
    
    try:
        test_clickup_connector_initialization()
        test_clickup_connector_headers()
        test_clickup_connector_api_request_validation()
        test_date_range_functionality()
        test_schema_validation()
        
        print("\n" + "=" * 50)
        print("✅ All tests passed!")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
