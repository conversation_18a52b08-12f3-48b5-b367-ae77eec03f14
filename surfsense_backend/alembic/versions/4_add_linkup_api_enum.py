"""Add LINKUP_API to SearchSourceConnectorType enum

Revision ID: 4
Revises: 3

"""

from collections.abc import Sequence

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "4"
down_revision: str | None = "3"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###

    # Manually add the command to add the enum value
    op.execute("ALTER TYPE searchsourceconnectortype ADD VALUE 'LINKUP_API'")

    # Pass for the rest, as autogenerate didn't run to add other schema details
    pass
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###

    # Downgrading removal of an enum value requires recreating the type
    op.execute(
        "ALTER TYPE searchsourceconnectortype RENAME TO searchsourceconnectortype_old"
    )
    op.execute(
        "CREATE TYPE searchsourceconnectortype AS ENUM('SERPER_API', 'TAVILY_API', 'SLACK_CONNECTOR', 'NOTION_CONNECTOR', 'GITHUB_CONNECTOR', 'LINEAR_CONNECTOR')"
    )
    op.execute(
        "ALTER TABLE search_source_connectors ALTER COLUMN connector_type TYPE searchsourceconnectortype USING "
        "connector_type::text::searchsourceconnectortype"
    )
    op.execute("DROP TYPE searchsourceconnectortype_old")

    pass
    # ### end Alembic commands ###
